/**
 * 商品折扣叠加系统
 *
 * 核心功能：
 * 1. 通过 Shopline API 获取商品自动折扣活动信息
 * 2. 在当前售价基础上叠加 API 折扣（非原价）
 * 3. 使用 MutationObserver 监控 DOM 变化，自动处理新增商品
 * 4. 支持产品卡片和产品详情页两种页面类型
 */

const LOG_STYLES = {
  prefix: '%c[折扣系统]',
  style: 'background-color: #424242; color: white; padding: 3px 8px; border-radius: 4px; font-weight: bold; font-size: 12px;',

  info: 'color: #2196F3; font-weight: bold;',
  success: 'color: #4CAF50; font-weight: bold;',
  warning: 'color: #FF9800; font-weight: bold;',
  error: 'color: #F44336; font-weight: bold;'
};

function cgLog(message, type = 'info', ...args) {
  const typeStyle = LOG_STYLES[type] || LOG_STYLES.info;
  console.log(LOG_STYLES.prefix + ' ' + message, LOG_STYLES.style + '; ' + typeStyle, ...args);
}

const productInfoMap = new Map();
let globalDiscountData = null;

const CONFIG = {
  API_URL: "https://caguuu.myshopline.com/storefront/graph/v20250601/graphql.json",
  API_TOKEN: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ5c291bCIsInRva2VuIjoiZGFlNWZmZTk0OGNiMjIwNGYzYzNjNTlkZjgwMGIzY2QzYWUyMGU1Y3w4ODUzNXwxNzI2NDY0MTkyNDI3fDE3NDY3NzE2NjB8MjA2MTU1Nzk2MnwifQ.eYX30QQX4a-hCvA8u_yhfMLzsCvn7DwzkFvLPeZZkSu05HlwuUTA4vXJ2DHpuDinVbLvM-mHeGgV0tUcbXW8Kg",

  SELECTORS: {
    /* 同时匹配两套组件：老的 card__* & 新的 recommend-product-item */
    BADGE: '.card__badge, .recommend-product-item-sale-tag',
    PRODUCT_CARD: '[data-product-id], .recommend-product-item',

    /* 价格容器 */
    PRICE_CONTAINER: '.price__container, .recommend-product-item-price',

    /* 现价 / 原价 / 省多少 */
    PRICE_SALE: '.price-item--sale,   .recommend-product-item-price .sale-price',
    PRICE_REGULAR: '.price-item--regular,.recommend-product-item-price .origin-price',
    PRICE_SAVE: '.price-item--save,   .recommend-product-item-price .save-price',
    PRODUCT_DETAIL: 'main-product-detail'
  },

  CURRENCY: {
    SYMBOL: '¥',
    LOCALE: 'ja-JP'
  }
};

function detectPageType() {
  const productDetailElement = document.querySelector(CONFIG.SELECTORS.PRODUCT_DETAIL);
  return productDetailElement ? 'product-detail' : 'product-list';
}

/**
 * 返回当前 productCard 所在的上下文：
 *  - 'detail'：商品详情页顶部信息区
 *  - 'card'  ：列表/轮播等普通商品卡
 */
function getProductContext(productCard) {
  return productCard.closest(CONFIG.SELECTORS.PRODUCT_DETAIL) ? 'detail' : 'card';
}

async function getBasicProductDiscounts(productId) {
  const headers = {
    'content-type': 'application/json',
    'authorization': CONFIG.API_TOKEN
  };

  const body = {
    query: `query GetBasicProductDiscounts($p1: ProductDiscountsInput!) {
  p1: productDiscounts(productDiscountsInput: $p1) {
    productId
    autoDiscountActivities {
      activityName
      activitySeq
      startsAt
      endsAt
      benefitConditions {
        benefit {
          discount
          promotionSeq
        }
        benefitEvent {
          minThreshold
        }
      }
    }
  }
}`,
    variables: {
      p1: {
        discountsProduct: {
          productId: `gid://shopline/Product/${productId}`,
        },
      },
    },
    operationName: "GetBasicProductDiscounts",
  };

  try {
    const response = await fetch(CONFIG.API_URL, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const extractedData = extractDiscountData(data);
    return extractedData;
  } catch (error) {
    cgLog("获取商品折扣时出错:", 'error', error);

    return {
      productId,
      discounts: [],
      summary: {
        totalActivities: 0,
        bestDiscount: 0
      },
      error: error.message
    };
  }
}

function extractDiscountData(responseData) {
  const p1 = responseData.data?.p1;
  if (!p1 || !p1.autoDiscountActivities) {
    return { discounts: [], productId: null };
  }

  const productId = p1.productId;
  const activities = p1.autoDiscountActivities;

  const extractedDiscounts = activities.map(activity => {
    const discountTiers = activity.benefitConditions.map(condition => ({
      minThreshold: parseInt(condition.benefitEvent.minThreshold),
      minThresholdText: parseInt(condition.benefitEvent.minThreshold) / 100,
      discount: condition.benefit.discount,
      discountText: `${100 - condition.benefit.discount}% OFF`
    })).sort((a, b) => a.minThreshold - b.minThreshold);

    return {
      activityName: activity.activityName,
      activitySeq: activity.activitySeq,
      discountStyleText: activity.discountStyleText,
      discountTiers: discountTiers
    };
  });

  return {
    productId,
    discounts: extractedDiscounts,
    summary: {
      totalActivities: activities.length,
      bestDiscount: Math.max(...activities.flatMap(a =>
        a.benefitConditions.map(c => c.benefit.discount)
      ))
    }
  };
}

// 从 DOM 中提取商品信息，适配产品卡片和产品详情页两种结构
function parseProductInfoFromDOM(productCard) {
  const productId =
    productCard.getAttribute('data-product-id') ||
    productCard.getAttribute('data-id');
  if (!productId) {
    cgLog('商品卡片缺少data-product-id属性', 'warning');
    return null;
  }
  if (!productCard.hasAttribute('data-product-id')) {
    productCard.setAttribute('data-product-id', productId);
  }

  const context = getProductContext(productCard);

  const titleElement = productCard.querySelector(
    '.product__title, .recommend-product-item-title'
  );
  const title = titleElement ? titleElement.textContent.trim() : '';

  /**
   * 详情页 price__container 不一定在 productCard 里，
   * 用 closest('main-product-detail') 向上找到大容器再去查
   */
  let priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
  if (!priceContainer && context === 'detail') {
    priceContainer = productCard
      .closest(CONFIG.SELECTORS.PRODUCT_DETAIL)  // 'main-product-detail'
      ?.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER) || null;
  }
  let originalPrice = 0;
  let salePrice = 0;
  let currentDiscount = 0;

  // 检测原始价格是否包含 ~ 符号
  let hasOriginalTildeSymbol = false;

  if (priceContainer) {
    const salePriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_SALE);
    if (salePriceElement) {
      const salePriceText = salePriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      salePrice = parseInt(salePriceText) || 0;
      // 检测是否包含 ~ 符号
      hasOriginalTildeSymbol = salePriceElement.textContent.includes('~');
    }

    const regularPriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_REGULAR);
    if (regularPriceElement) {
      const regularPriceText = regularPriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      originalPrice = parseInt(regularPriceText) || 0;
      // 如果现价元素没有 ~ 符号，检查原价元素
      if (!hasOriginalTildeSymbol) {
        hasOriginalTildeSymbol = regularPriceElement.textContent.includes('~');
      }
    }
  }

  // --- 兜底逻辑，两种情况 ---
  // ① 只有现价行（本身已打折）：把现价当原价
  if (originalPrice === 0 && salePrice > 0) {
    originalPrice = salePrice;
  }

  // ② 只有原价行（本身未打折）：把原价当现价
  if (salePrice === 0 && originalPrice > 0) {
    salePrice = originalPrice;
  }

  if (originalPrice > 0 && salePrice > 0 && salePrice < originalPrice) {
    currentDiscount = Math.round((1 - salePrice / originalPrice) * 100);
  }

  const badgeElement = productCard.querySelector(CONFIG.SELECTORS.BADGE);
  let badgeText = '';
  if (badgeElement) {
    badgeText = badgeElement.textContent.trim();
  }

  const saveElement = priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE);

  return {
    productId,
    title,
    originalPrice,
    salePrice,
    currentDiscount,
    badgeText,
    context,
    hasOriginalTildeSymbol,
    domElements: {
      productCard,
      priceContainer,
      badgeElement,
      saveElement,
      salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
      regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
    }
  };
}

// 核心折扣计算逻辑：API 折扣叠加在当前售价基础上，而非原价
function calculateDiscountedPrices(productInfo, discountData) {
  if (!discountData.discounts || discountData.discounts.length === 0) {
    cgLog(`商品ID ${productInfo.productId}: 没有可用的API折扣`);
    return {
      finalSalePrice: productInfo.salePrice,
      finalOriginalPrice: productInfo.originalPrice,
      finalDiscount: productInfo.currentDiscount,
      appliedDiscounts: [],
      hasApiDiscount: false
    };
  }

  let bestApiDiscount = 0;
  let appliedDiscounts = [];
  let appliedTier = null;

  const productPrice = productInfo.salePrice;
  cgLog(`正在计算商品ID ${productInfo.productId}的API折扣。当前售价: ${formatPriceHalfYen(productPrice)} (${productPrice})`);

  // 查找适用的最佳折扣梯度
  discountData.discounts.forEach(discount => {
    const sortedTiers = [...discount.discountTiers].sort((a, b) => b.minThreshold - a.minThreshold);

    // minThreshold 以分为单位，需要转换商品价格进行比较
    const productPriceInCents = productPrice * 100;

    for (const tier of sortedTiers) {
      if (productPriceInCents >= tier.minThreshold) {
        // API返回的discount字段是保留比例(如85表示85%)，需转换为折扣比例(15%)
        const discountPercent = 100 - tier.discount;
        if (discountPercent > bestApiDiscount) {
          bestApiDiscount = discountPercent;
          appliedTier = tier;
          appliedDiscounts = [{
            activityName: discount.activityName,
            discountPercent: discountPercent,
            discountText: tier.discountText,
            minThreshold: tier.minThreshold,
            minThresholdText: tier.minThresholdText
          }];
        }
        // 找到第一个满足条件的梯度后立即跳出，避免被低门槛梯度覆盖
        break;
      }
    }
  });

  // 关键：在当前售价基础上应用 API 折扣，然后计算相对于原价的总折扣率
  let finalSalePrice = productInfo.salePrice;
  let hasApiDiscount = false;

  if (bestApiDiscount > 0 && appliedTier) {
    // 在当前售价基础上应用 API 折扣，向下取整
    finalSalePrice = Math.floor(productInfo.salePrice * (100 - bestApiDiscount) / 100);
    hasApiDiscount = true;

    cgLog(`已应用API折扣: ${productInfo.salePrice} * (1 - ${bestApiDiscount}%) = ${finalSalePrice} (最低门槛: ${appliedTier.minThresholdText}円)`, 'success');
  } else {
    cgLog(`商品价格 ${productInfo.salePrice} (${formatPriceHalfYen(productInfo.salePrice)}) 没有适用的折扣`);
  }

  // 计算相对于绝对原价的总折扣率
  let finalDiscount = 0;
  if (productInfo.originalPrice > 0 && finalSalePrice < productInfo.originalPrice) {
    finalDiscount = Math.round((productInfo.originalPrice - finalSalePrice) / productInfo.originalPrice * 100);
  }

  return {
    finalSalePrice,
    finalOriginalPrice: productInfo.originalPrice,
    finalDiscount,
    appliedDiscounts,
    hasApiDiscount,
    debugInfo: {
      originalPrice: productInfo.originalPrice,
      currentSalePrice: productInfo.salePrice,
      currentDiscount: productInfo.currentDiscount,
      apiDiscountPercent: bestApiDiscount,
      appliedThreshold: appliedTier ? appliedTier.minThreshold : null,
      finalSalePrice,
      totalDiscountPercent: finalDiscount
    }
  };
}

function formatPriceHalfYen(price) {
  const halfYen = '\u00A5'; // 半角日圆符号
  const nf = new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0
  });

  // 拆分为若干部分，然后把 currency 部分替换
  return nf
    .formatToParts(price)
    .map(part => part.type === 'currency' ? halfYen : part.value)
    .join('');
}

/**
 * 把折扣数据写回 DOM
 * ① 无论商品本身是否已有折扣，都能生成统一结构
 * ② 根据 context = card / detail 写入不同的 class 层次
 * ③ 已存在节点会复用；缺失节点即刻动态创建
 */
function updateProductCardDisplay(productInfo, priceData) {
  const { domElements, context } = productInfo;

  /* ── ① 推荐商品卡（recommend-product-item）专用分支 ───────────── */
  if (domElements.productCard.classList.contains('recommend-product-item')) {
    updateRecommendPriceBox(productInfo, priceData);   // ⇠ 新函数，见下
    domElements.productCard.setAttribute('data-discount-updated', '1');
    return;                                           // ← 直接结束，不走后面的老逻辑
  }

  /* === 0. 若无可写入的折扣，或价格不变，就直接返回 === */
  if (
    !priceData.hasApiDiscount ||
    priceData.finalSalePrice === productInfo.salePrice
  ) return;

  // ① 若页面已有隐藏的 .card__badge，直接复用
  if (!domElements.badgeElement) {
    domElements.badgeElement =
      domElements.productCard.querySelector('.card__badge');
    if (domElements.badgeElement) domElements.badgeElement.style.display = '';
  }

  // ② 仍找不到就新建，并**插到 .card__inner.ratio** 里
  if (!domElements.badgeElement || !domElements.badgeElement.querySelector('span')) {
    const badge = document.createElement('div');
    badge.className = 'card__badge left_top card__badge--mini';

    const span = document.createElement('span');
    span.className = 'body5 fw-bold';
    if (context === 'card') { badge.appendChild(span); }

    // ratio 上挂着 --badge-border-radius，优先插这里
    const ratio = domElements.productCard.querySelector(
      '.card__inner.ratio, .recommend-product-item-image'
    );
    if (context === 'card') { (ratio || domElements.productCard).appendChild(badge); }

    domElements.badgeElement = badge;
  }

  // ③ 写入折扣文字
  const badgeSpan = domElements.badgeElement.querySelector('span');
  if (badgeSpan) badgeSpan.textContent = `${priceData.finalDiscount}％OFF`;

  /* === 2. 价格容器准备 ========================================== */
  let { priceContainer } = domElements;

  // priceContainer 可能在切 SKU 的瞬间被删掉；实时兜底一次
  if (!priceContainer) {
    priceContainer = domElements.productCard
      .querySelector(CONFIG.SELECTORS.PRICE_CONTAINER) ||
      domElements.productCard
        .closest(CONFIG.SELECTORS.PRODUCT_DETAIL)
        ?.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER) || null;
    domElements.priceContainer = priceContainer;
  }

  // 仍然拿不到 ⇒ 说明价格区块还没渲染好，留给下一轮 MutationObserver
  if (!priceContainer) {
    cgLog('priceContainer 尚未就绪，等待下一轮 mutation', 'warning');
    return;
  }

  // 原价 span（一定要有）
  let regularSpan = domElements.regularPriceElement;
  if (!regularSpan) {
    regularSpan = document.createElement('span');
    regularSpan.className = context === 'detail' ? 'fw-bold price-item--regular font_size_small' : 'fw-bold price-item--regular';
    regularSpan.textContent = formatPriceHalfYen(priceData.finalOriginalPrice);
    // 临时塞到最前，稍后可能被移动
    const regularWrap = document.createElement('div');
    regularWrap.className = 'price__regular body2';
    regularWrap.appendChild(regularSpan);
    priceContainer.prepend(regularWrap);
    domElements.regularPriceElement = regularSpan;
  } else {
    if (context === 'detail' && regularSpan.classList.contains('font_size_medium')) {
      regularSpan.classList.remove('font_size_medium');
      regularSpan.classList.add('font_size_small');
    };
  }

  // 如果还不存在 sale 结构，则整套创建
  if (!domElements.salePriceElement) {
    const saleWrap = document.createElement('div');
    saleWrap.className = 'price__sale' + (context === 'detail' ? ' save_sale_origin' : '');

    // --- 2.1 现价 ---
    const salePos = document.createElement('span');
    salePos.className = 'body2 price-position-sale';
    const saleSpan = document.createElement('span');
    saleSpan.className = context === 'detail' ? 'fw-bold price-item price-item--sale font_size_medium' : 'fw-bold price-item price-item--sale';
    salePos.appendChild(saleSpan);
    saleWrap.appendChild(salePos);
    domElements.salePriceElement = saleSpan;

    // --- 2.2 原价 ---
    const originPos = document.createElement('span');
    originPos.className = 'body5 price-position-origin';
    originPos.appendChild(regularSpan);       // 把原价 span 移进来
    saleWrap.appendChild(originPos);

    // --- 2.3 节省百分比（仅详情页需要） ---
    if (context === 'detail') {
      const savePos = document.createElement('span');
      savePos.className = 'body5 price-position-save';
      const saveSpan = document.createElement('span');
      saveSpan.className = 'price-item--save price-item--save-button';
      savePos.appendChild(saveSpan);
      saleWrap.appendChild(savePos);
      domElements.saveElement = saveSpan;
    }

    // 把新的 saleWrap 放到容器顶
    priceContainer.prepend(saleWrap);

    // 移除老的 .price__regular 包裹（如果还在 DOM）
    const oldReg = priceContainer.querySelector('.price__regular');
    if (oldReg) oldReg.remove();
  }

  /* === 3. 写入价格数字 / 折扣文字 ================================ */
  const formattedSale = formatPriceHalfYen(priceData.finalSalePrice);
  const taxInfo = domElements.salePriceElement.querySelector('p');
  const taxHTML = taxInfo
    ? taxInfo.outerHTML
    : '<p style="font-size:0.7em;font-weight:normal;margin:0;">税込</p>';
  // 根据页面原本是否有 ~ 符号来决定是否添加
  const suffix = productInfo.hasOriginalTildeSymbol ? ' ~' : '';
  domElements.salePriceElement.innerHTML =
    `${formattedSale}${taxHTML}${suffix}`;

  domElements.regularPriceElement.textContent =
    formatPriceHalfYen(priceData.finalOriginalPrice);

  if (context === 'detail' && domElements.saveElement) {
    domElements.saveElement.textContent = `${priceData.finalDiscount}%OFF`;
  }

  /* === 4. 标记已完成，避免重复 ================================ */
  domElements.productCard.setAttribute('data-discount-updated', '1');
  cgLog(`已更新${context}商品 ${productInfo.productId}: ` +
    `${formattedSale} (${priceData.finalDiscount}% 折扣)`, 'success');
}

/**
 * 针对 .recommend-product-item 的价格区就地更新
 * - 若无折扣，只保留 origin-price
 * - 若有折扣，生成 origin-price / sale-price / save-price
 */
function updateRecommendPriceBox(productInfo, priceData) {
  const { domElements } = productInfo;
  const box = domElements.priceContainer;                 // .recommend-product-item-price
  if (!box) return;

  const noDiscount =
    priceData.finalDiscount === 0 ||
    priceData.finalSalePrice >= priceData.finalOriginalPrice;

  /* ---------- ① 确保 origin-price 存在 ---------- */
  let originOuter = box.querySelector('.origin-price');
  if (!originOuter) {
    originOuter = document.createElement('span');
    originOuter.className = 'origin-price notranslate';
    box.insertBefore(originOuter, box.firstChild);        // 放最前
  }

  // 内层 isolate span（若无则直接把文字写在外层）
  let isolateSpan = originOuter.querySelector('.isolate');
  if (!isolateSpan) {
    isolateSpan = document.createElement('span');
    isolateSpan.className = 'notranslate isolate';
    originOuter.appendChild(isolateSpan);
  }
  isolateSpan.textContent =
    `${formatPriceHalfYen(priceData.finalOriginalPrice)} `;  // 末尾空格保留页面原样式
  originOuter.setAttribute(
    'data-product-item-price',
    priceData.finalOriginalPrice
  );

  /* ---------- ② 如果没有折扣：移除 sale / save 并返回 ---------- */
  if (noDiscount) {
    // 1. 现价
    let saleSpan = box.querySelector('.sale-price');
    if (!saleSpan) {
      saleSpan = document.createElement('span');
      saleSpan.className = 'sale-price ';
      box.appendChild(saleSpan);
    }
    saleSpan.textContent = ` ${formatPriceHalfYen(priceData.finalSalePrice)} `;

    // 2. 清理原价 / 节省百分比
    box.querySelectorAll('.origin-price, .save-price').forEach(el => el.remove());

    return;                                           // 只显示原价
  }

  /* ---------- ③ 现价 sale-price ---------- */
  let saleSpan = box.querySelector('.sale-price');
  if (!saleSpan) {
    saleSpan = document.createElement('span');
    saleSpan.className = 'sale-price ';
    box.appendChild(saleSpan);                           // 在 origin-price 后自然排列
  }
  saleSpan.textContent = ` ${formatPriceHalfYen(priceData.finalSalePrice)} `;

  /* ---------- ④ 节省百分比 save-price ---------- */
  let saveSpan = box.querySelector('.save-price');
  if (!saveSpan) {
    saveSpan = document.createElement('span');
    saveSpan.className = 'save-price';
    box.appendChild(saveSpan);
  }
  saveSpan.textContent = `${priceData.finalDiscount}% OFF`;
}

// 处理单个商品的折扣更新，支持缓存复用和轮播/无限滚动场景
async function processProductDiscount(productCard) {
  try {
    const productInfo = parseProductInfoFromDOM(productCard);
    if (!productInfo) return;
    cgLog(`正在处理商品 ${productInfo.productId} (上下文: ${productInfo.context})...`);

    const cached = productInfoMap.get(productInfo.productId);

    /**
     * 1️⃣ 价格是否真的变了？
     *    - DOM 中读取到的现价 / 原价 ≠ 上次缓存里的 *final* 现价 / *final* 原价
     *      → 说明店铺脚本切 SKU 了，需要重新计算折扣
     * 2️⃣ 价格没变，但有缓存 → 可以直接复用，省一次计算
     */
    const priceReallyChanged =
      !cached ||
      productInfo.salePrice !== cached?.finalSalePrice ||
      productInfo.originalPrice !== cached?.finalOriginalPrice;

    if (cached && cached.hasApiDiscount && !priceReallyChanged) {
      if (!productCard.hasAttribute('data-discount-updated')) {
        const priceData = {
          finalSalePrice: cached.finalSalePrice,
          finalOriginalPrice: cached.finalOriginalPrice,
          finalDiscount: cached.finalDiscount,
          appliedDiscounts: cached.appliedDiscounts,
          hasApiDiscount: true
        };

        const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
        const domElements = {
          productCard,
          priceContainer,
          badgeElement: productCard.querySelector(CONFIG.SELECTORS.BADGE),
          saveElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE),
          salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };

        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        productCard.setAttribute('data-discount-updated', '1');
      }
      return;
    }

    // 走到这里 ⇒ 要么没缓存，要么价格确实换了（切换了 SKU）
    // 如果是后者，需要把所有克隆卡片的 “已更新” 标记先清掉
    if (cached && priceReallyChanged) {
      document
        .querySelectorAll(`[data-product-id="${productInfo.productId}"]`)
        .forEach(card => card.removeAttribute('data-discount-updated'));
    }

    // 重新计算 / 首次计算
    const priceData = calculateDiscountedPrices(productInfo, globalDiscountData);

    // 批量更新所有同款商品卡片，解决轮播组件克隆多个相同商品的问题
    // 使用:not([data-discount-updated])避免重复处理已更新的卡片
    document
      .querySelectorAll(`[data-product-id="${productInfo.productId}"]:not([data-discount-updated])`)
      .forEach(card => {
        const priceContainer = card.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
        const domElements = {
          productCard: card,
          priceContainer,
          badgeElement: card.querySelector(CONFIG.SELECTORS.BADGE),
          saveElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SAVE),
          salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };
        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        card.setAttribute('data-discount-updated', '1');
      });

    // 缓存结果供后续复用
    productInfoMap.set(productInfo.productId, {
      ...productInfo,
      ...priceData,
      lastUpdated: Date.now()
    });

    cgLog(
      `已对商品 ${productInfo.productId} 应用${priceData.hasApiDiscount ? 'API' : '现有'}折扣`, 'success'
    );
  } catch (err) {
    cgLog('处理商品折扣时出错:', 'error', err);
  }
}

function findProductCard(element) {
  let current = element;
  while (current && current !== document.body) {
    if (
      (current.hasAttribute && current.hasAttribute('data-product-id')) ||
      current.classList?.contains('recommend-product-item')
    ) {
      return current;
    }
    current = current.parentElement;
  }
  return null;
}

function handleBadgeChange(badgeElement) {
  const productCard = findProductCard(badgeElement);
  if (productCard) {
    processProductDiscount(productCard);
  }
}

function handlePriceChange(priceElement) {
  const productCard = findProductCard(priceElement);
  if (productCard) {
    processProductDiscount(productCard);
  }
}

// MutationObserver 回调：监控 DOM 变化，自动处理新增商品
function handleMutations(mutations) {
  // 使用Set防止同一个商品卡片在一次mutation中被重复处理
  const processedCards = new Set();

  mutations.forEach(mutation => {
    if (mutation.type === 'childList') {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const el = /** @type {HTMLElement} */ (node);

          /* 🅐 节点本身就是徽标 / 价格容器 */
          if (el.matches(CONFIG.SELECTORS.BADGE)) handleBadgeChange(el);
          if (el.matches(CONFIG.SELECTORS.PRICE_CONTAINER)) handlePriceChange(el);

          /* 🅑 递归检查子节点 */
          const badgeElements = el.querySelectorAll(CONFIG.SELECTORS.BADGE);
          if (badgeElements) {
            badgeElements.forEach(badge => {
              const productCard = findProductCard(badge);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handleBadgeChange(badge);
              }
            });
          }

          const priceContainers = el.querySelectorAll(CONFIG.SELECTORS.PRICE_CONTAINER);
          if (priceContainers) {
            priceContainers.forEach(priceContainer => {
              const productCard = findProductCard(priceContainer);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handlePriceChange(priceContainer);
              }
            });
          }
        }
      });
    }

    if (mutation.type === 'characterData' || mutation.type === 'childList') {
      const target = mutation.target;

      /* -- 徽标变动 ------------------------- */
      const badgeElement =
        target.nodeType === Node.ELEMENT_NODE && target.matches(CONFIG.SELECTORS.BADGE)
          ? target
          : target.closest?.(CONFIG.SELECTORS.BADGE);

      if (badgeElement) {
        const productCard = findProductCard(badgeElement);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handleBadgeChange(badgeElement);
        }
      }

      /* -- 价格区变动 ----------------------- */
      const priceContainer =
        target.nodeType === Node.ELEMENT_NODE && target.matches(CONFIG.SELECTORS.PRICE_CONTAINER)
          ? target
          : target.closest?.(CONFIG.SELECTORS.PRICE_CONTAINER);

      if (priceContainer) {
        const productCard = findProductCard(priceContainer);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handlePriceChange(priceContainer);
        }
      }
    }
  });
}

function initializeMutationObserver() {
  const observer = new MutationObserver(handleMutations);

  const config = {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: false
  };

  observer.observe(document.body, config);

  cgLog('已初始化MutationObserver用于监控card__badge和价格元素', 'success');
  return observer;
}

function processExistingProducts() {
  const productCards = document.querySelectorAll(CONFIG.SELECTORS.PRODUCT_CARD);
  const pageType = detectPageType();
  cgLog(`在${pageType}页面上找到${productCards.length}个现有商品卡片。开始初始处理。`);

  productCards.forEach(productCard => {
    const context = getProductContext(productCard);
    if (context === 'detail') {
      const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
      if (priceContainer) {
        processProductDiscount(productCard);
      }
    } else {
      const badge = productCard.querySelector(CONFIG.SELECTORS.BADGE);
      if (badge) {
        processProductDiscount(productCard);
      }
    }
  });
}

async function initializeDiscountSystem() {
  cgLog('正在初始化折扣系统...', 'info');

  // 使用固定测试商品ID获取全局折扣配置，所有商品共享同一套折扣规则
  // 这样避免了为每个商品单独请求API，提高性能
  const testProductId = '16069927806303341599314095';
  globalDiscountData = await getBasicProductDiscounts(testProductId);
  cgLog('已获取全局折扣数据:', 'success', globalDiscountData);

  processExistingProducts();
  const observer = initializeMutationObserver();

  return observer;
}

let discountObserver = null;

function stopDiscountSystem() {
  if (discountObserver) {
    discountObserver.disconnect();
    discountObserver = null;
    cgLog('折扣系统已停止', 'warning');
  }
}

async function restartDiscountSystem() {
  stopDiscountSystem();
  discountObserver = await initializeDiscountSystem();
}

function clearProductCache() {
  productInfoMap.clear();
  cgLog('商品缓存已清除', 'warning');
}

function forceReprocessAllProducts() {
  clearProductCache();
  processExistingProducts();
  cgLog('所有商品已重新处理', 'success');
}

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    discountObserver = await initializeDiscountSystem();
  });
} else {
  (async () => {
    discountObserver = await initializeDiscountSystem();
  })();
}

// 暴露调试接口
window.DiscountSystem = {
  init: async () => await initializeDiscountSystem(),
  stop: stopDiscountSystem,
  restart: restartDiscountSystem,
  processExisting: processExistingProducts,
  clearCache: clearProductCache,
  forceReprocess: forceReprocessAllProducts,
  productMap: productInfoMap,
  config: CONFIG,
  getProductInfo: (productId) => productInfoMap.get(productId),
  getAllProducts: () => Array.from(productInfoMap.entries()),
  getStats: () => ({
    totalProducts: productInfoMap.size,
    processedWithApiDiscount: Array.from(productInfoMap.values()).filter(p => p.hasApiDiscount).length
  })
};

cgLog('折扣系统已加载。使用window.DiscountSystem进行调试。', 'success');