<script src="{{asset_url 'component-slider.js'}}" defer></script>
{{snippet "stylesheet" href=(asset_url "section-collection-list.css")}}
{{snippet "stylesheet" href=(asset_url "component-card.css")}}
{{snippet "stylesheet" href=(asset_url "snippet-collection-card.css")}}

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{assign "title" section.settings.title}}
{{assign "button_text" section.settings.button_text}}
{{assign "mobile_grid_cols" section.settings.m_cols}}
{{assign "desktop_grid_cols" section.settings.desktop_grid_cols}}
{{assign "color_scheme" section.settings.color_scheme}}
{{assign "ratio" section.settings.collection_image_ratio}}
{{assign "max_in_mobile" section.settings.max_in_mobile}}
{{assign "scroll_pc" section.settings.slice_in_pc}}
{{assign "scroll_mobile" section.settings.slice_in_mobile}}
{{assign "image_shape" section.settings.collection_image_shape}}
{{assign "mobile_grid_rows" section.settings.m_rows}}

{{assign "pag_size" (times mobile_grid_cols mobile_grid_rows)}}
{{assign "block_data" section.blocks}}
{{assign "pag_total" (ternary (if max_in_mobile >= (size section.blocks)) (size section.blocks) max_in_mobile)}}
{{assign "page_num" (ceil (divide pag_total pag_size))}}

{{#if scroll_mobile and mobile_grid_rows >= 2 and pag_total > mobile_grid_cols}}
  {{assign "is_rows_scroll" true}}
{{/if}}

{{#if scroll_pc or scroll_mobile}}
  {{assign "is_scroll" true}}
{{/if}}
<div class="color-scheme-{{color_scheme}}">
  <div class="page-width text-center section-padding">
    <div class="collection-list__header">
      <h4 class="title5 title-wrapper">
        {{title}}
      </h4>
      {{#if (isTruthy button_text)}}
        <div class="collection-list-more-btn">
          <a class="body2 button button--link viewall__button" href="/pages/special#shopline-section-1740634905286447dfcb">
            {{button_text}}
          </a>
        </div>
      {{/if}}
    </div>
    <slider-component class="slider-collection-list">
      <div
        id="Slider-collection_list"
        class="grid
          grid-cols-{{#if section.blocks.length <= desktop_grid_cols}}{{section.blocks.length}}{{else}}{{desktop_grid_cols}}{{/if}}-desktop
          grid-cols-{{mobile_grid_cols}}-tablet collection-list__slider__container contains-collection-card-slider slider
          {{#if scroll_pc}}slider--desktop{{/if}}
          {{#if scroll_mobile}}slider--mobile mobile-slider-full-screen{{/if}}
          {{#if (isTruthy is_rows_scroll)}}display-none-tablet{{/if}}
          "
        style="--mobile-cols:{{mobile_grid_cols}};"
      >
        {{#for section.blocks as |block|}}
          {{assign "title" block.settings.title}}
          {{assign "image_position" block.settings.image_display_area}}
          {{assign "card_collection" block.settings.category}}
          {{#if forloop.index0 >= max_in_mobile}}
            {{assign "class" "display-none-tablet"}}
          {{/if}}
          {{snippet
            "collection-card"
            block=block
            card_collection=card_collection
            title=title
            ratio=ratio
            position=image_position
            desktop_grid_cols=desktop_grid_cols
            mobile_grid_cols=mobile_grid_cols
            image_shape=image_shape
            is_scroll=is_scroll
            class=class
            blockId=block.id
          }}
        {{/for}}
      </div>
      {{assign "length" (length section.blocks)}}
      {{#if length > desktop_grid_cols and scroll_pc}}
        <button name="previous" class="colletionlist__arrow display-none display-flex-desktop">
          {{snippet "icon-arrow"}}
        </button>
        <button name="next" class="colletionlist__arrow colletionlist__arrow--right display-none display-flex-desktop">
          {{snippet "icon-arrow"}}
        </button>
      {{/if}}
    </slider-component>
    
    {{#if is_rows_scroll}}
      <slider-component class="slider-collection-list">
        <div
          id="Slider-collection-list-group-{{section.id}}"
          class="collection_list_group collection-list__slider__container contains-collection-card-slider slider display-none-desktop slider--mobile"
          style="--mobile-cols:{{mobile_grid_cols}}"
        >
          {{#for block_data limit=pag_total as |block|}}
            {{#if (modulo forloop.index0 pag_size) == 0}}
              <div class="slider__slide grid grid-cols-{{mobile_grid_cols}}-tablet" id="Slide-{{block.id}}">
            {{/if}}
            {{assign "title" block.settings.title}}
            {{assign "image_position" block.settings.image_display_area}}
            {{assign "card_collection" block.settings.category}}
            {{snippet
              "collection-card"
              block=block
              card_collection=card_collection
              title=title
              ratio=ratio
              position=image_position
              mobile_grid_cols=mobile_grid_cols
              image_shape=image_shape
              is_scroll=false
              class=""
              blockId=block.id
            }}
            {{#if (modulo (plus forloop.index0 1) pag_size) == 0 or (plus forloop.index0 1) == pag_total}}
              </div>
            {{/if}}
          {{/for}}
        </div>
        {{#if page_num > 1}}
          <div
            class="slide-pagination display-none-desktop"
            style="--pagination-background-color:var(--color-scheme-{{color_scheme}}-text);"
          >
            {{#for block_data limit=page_num}}
              <button class="slide-pagination-bullet" name="pager" data-index="{{forloop.index0}}"></button>
            {{/for}}
          </div>
        {{/if}}
      </slider-component>
    {{/if}}
    
  </div>
</div>
{{#schema}}
{
  "name": "t:sections.collection-list.name",
  "class": "section",
  "presets": [
    {
      "category_index": 2,
      "category": "t:sections.collection-list.presets.presets__0.category",
      "name": "t:sections.collection-list.presets.presets__0.name",
      "settings": {
        "title": "Collection list",
        "collection_image_ratio": "100",
        "color_scheme": "none",
        "slice_in_mobile": false,
        "slice_in_pc": false,
        "max_in_mobile": 6,
        "button_text": "View all",
        "desktop_grid_cols": 7,
        "m_cols": 3,
        "padding_top": 80,
        "padding_bottom": 80
      },
      "blocks": [
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        },
        {
          "type": "collection",
          "settings": {
            "image_display_area": "center center"
          }
        }
      ]
    }
  ],
  "max_blocks": 16,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.collection-list.settings.title.label",
      "default": "Collection list"
    },
    {
      "id": "collection_image_ratio",
      "type": "select",
      "label": "t:sections.collection-list.settings.collection_image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.collection-list.settings.collection_image_ratio.options__0.label"
        },
        {
          "value": "100",
          "label": "t:sections.collection-list.settings.collection_image_ratio.options__1.label"
        },
        {
          "value": "75",
          "label": "t:sections.collection-list.settings.collection_image_ratio.options__2.label"
        },
        {
          "value": "150",
          "label": "t:sections.collection-list.settings.collection_image_ratio.options__3.label"
        }
      ],
      "default": "100"
    },
    {
      "id": "collection_image_shape",
      "type": "select",
      "label": "t:sections.collection-list.settings.collection_image_shape.label",
      "options": [
        {
          "value": "square",
          "label": "t:sections.collection-list.settings.collection_image_shape.options__0.label"
        },
        {
          "value": "round",
          "label": "t:sections.collection-list.settings.collection_image_shape.options__1.label"
        }
      ],
      "default": "square"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.collection-list.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collection-list.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.collection-list.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.collection-list.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.collection-list.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "id": "desktop_grid_cols",
      "type": "range",
      "label": "t:sections.collection-list.settings.desktop_grid_cols.label",
      "default": 7,
      "min": 1,
      "max": 8,
      "step": 1
    },
    {
      "id": "m_cols",
      "type": "select",
      "label": "t:sections.collection-list.settings.m_cols.label",
      "default": 1,
      "options": [
        {
          "value": 1,
          "label": "t:sections.collection-list.settings.m_cols.options__0.label"
        },
        {
          "value": 2,
          "label": "t:sections.collection-list.settings.m_cols.options__1.label"
        },
        {
          "value": 3,
          "label": "t:sections.collection-list.settings.m_cols.options__2.label"
        },
        {
          "value": 4,
          "label": "t:sections.collection-list.settings.m_cols.options__3.label"
        }
      ]
    },
    {
      "id": "m_rows",
      "type": "select",
      "label": "t:sections.collection-list.settings.m_rows.label",
      "info": "t:sections.collection-list.settings.m_rows.info",
      "default": 1,
      "options": [
        {
          "value": 1,
          "label": "t:sections.collection-list.settings.m_rows.options__0.label"
        },
        {
          "value": 2,
          "label": "t:sections.collection-list.settings.m_rows.options__1.label"
        },
        {
          "value": 3,
          "label": "t:sections.collection-list.settings.m_rows.options__2.label"
        }
      ]
    },
    {
      "type": "switch",
      "id": "slice_in_mobile",
      "label": "t:sections.collection-list.settings.slice_in_mobile.label",
      "default": false
    },
    {
      "type": "switch",
      "id": "slice_in_pc",
      "label": "t:sections.collection-list.settings.slice_in_pc.label",
      "default": false
    },
    {
      "type": "range",
      "id": "max_in_mobile",
      "label": "t:sections.collection-list.settings.max_in_mobile.label",
      "default": 16,
      "max": 16,
      "min": 3
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "t:sections.collection-list.settings.button_text.label",
      "default": "View all"
    },
    {
      "type": "group_header",
      "label": "t:sections.collection-list.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.collection-list.settings.padding_top.label",
      "default": 80,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.collection-list.settings.padding_bottom.label",
      "default": 80,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "icon": "normal",
      "name": "t:sections.collection-list.blocks.collection.name",
      "settings": [
        {
          "type": "collection_picker",
          "id": "category",
          "label": "t:sections.collection-list.blocks.collection.settings.category.label"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.collection-list.blocks.collection.settings.title.label"
        },
        {
          "type": "select",
          "id": "image_display_area",
          "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.label",
          "info": "t:sections.collection-list.blocks.collection.settings.image_display_area.info",
          "default": "center center",
          "options": [
            {
              "value": "left top",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__0.label"
            },
            {
              "value": "center top",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__1.label"
            },
            {
              "value": "right top",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__2.label"
            },
            {
              "value": "center left",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__3.label"
            },
            {
              "value": "center center",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__4.label"
            },
            {
              "value": "center right",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__5.label"
            },
            {
              "value": "left bottom",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__6.label"
            },
            {
              "value": "center bottom",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__7.label"
            },
            {
              "value": "right bottom",
              "label": "t:sections.collection-list.blocks.collection.settings.image_display_area.options__8.label"
            }
          ]
        }
      ]
    }
  ]
}
{{/schema}}