<!DOCTYPE html>
<html lang="{{request.locale.iso_code}}">
  <head>
  <meta name="p:domain_verify" content="3e09bc2da62d0d2bacd817c043bcd26f" />
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <link rel="canonical" href="{{canonical_url}}" />
    <meta http-equiv="x-dns-prefetch-control" content="on" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />

    {{#if settings.favicon_image}}
      <link rel="icon" type="image/png" href="{{settings.favicon_image}}" />
    {{/if}}

    {{snippet "title-tag"}}

    {{#if page_description}}
      <meta name="description" content="{{page_description}}" />
    {{/if}}

    {{#if page_keyword}}
      <meta name="keywords" content="{{page_keyword}}" />
    {{/if}}

    {{snippet "meta-tags"}}

    {{snippet "theme-css-var"}}

    {{snippet "stylesheet" href=(asset_url "base.css")}}

    <script src="{{asset_url 'global.js'}}" defer="defer"></script>

    <!-- afb tag -->
    <script>
    if (!window.afblpcvLpConf) {
    window.afblpcvLpConf = [];
    }
    window.afblpcvLpConf.push({
    siteId: "f13e1570"
    });
    </script>
    <script async src="https://t.afi-b.com/jslib/lpcv.js?cid=f13e1570&pid=n15419z" async="async"></script>
    <!-- end afb tag -->
    <!-- atw tag -->
    <script async src="https://h.accesstrade.net/js/nct/lp.min.js"></script>
    <!-- end atw tag -->
    <!-- toridori tag -->
    <script async src="https://static.appront.net/click.js"></script>
    <!-- end toridori tag -->
    <!-- a8sales tag -->
    <script async src="//statics.a8.net/a8sales/a8sales.js"></script>
    <script async src="//statics.a8.net/a8sales/a8crossDomain.js "></script>
    <!-- end a8sales tag -->

    {{content_for_header}}
  </head>
  <body data-template="{{template.name}}" data-button-hover-animation="{{settings.btn_hover_animation}}">
    {{section "announcement-bar"}}
    {{section "header"}}

    {{#if settings.cart_type == "drawer" and template.name != "cart"}}
      <cart-drawer-entry class="cart-drawer-entry">
        {{snippet "cart-drawer"}}
      </cart-drawer-entry>
    {{/if}}

    <main id="MainContent" class="content-for-layout" tabindex="-1">
      {{content_for_layout}}
    </main>

    {{section "footer"}}
    {{content_for_footer}}
<style>
a.button.button--secondary.button--full-width:after {
    box-shadow: 0 0 0 var(--btn-border-thickness) rgba(var(--color-button-text), var(--border-opacity)), 0 0 0 var(--btn-border-thickness) rgba(var(--color-button-background), var(--alpha-button-background)) !important;
}
.advc .advc-footer-tab,#shopline-section-header{
	z-index:100 !important;
}
.advc .advc-footer-tab .advc-footer-tab__item .advc-footer-tab__title {
    width: 120%;
}
.portal-0>div {
    top: 50%;
}
.cart-sku-list-promotion-module {
    font-family: var(--body-font);
}
/* 超级菜单 */
._navMenuRight_1vlwj_67 .mega-block-container {
    height: auto !important;
}
._nevMenu_1vlwj_29 {
    height: 600px !important;
}
/* chatbox位置 */
.preview-content-button-mobile--Mw_Wl {
    bottom: 64px;
}
@media only screen and (max-width: 750px) {
.preview-content-button-mobile--Mw_Wl.preview-button-left {
    left: 20px;
}
}
.preview-content-button--wDAni .chat-widget-button-circle,.chat-widget-close-button--mwhzz {
    height: 50px;
    width: 50px;
}
/* 弹窗样式改变 */
.portal-0>div>div{
/* ._container_et3n4_12{ */
width:auto !important;
max-width: none !important;
background-color: rgb(255, 255, 255, 0.8) !important;
margin-left: 20px;
backdrop-filter: blur(6px);
border-radius: 50px !important;
padding:0 16px !important;
gap:10px;
}
.portal-0>div>div>span:nth-child(2){
/* ._closeBtn_et3n4_73{ */
display:none !important;
}
.portal-0>div>div>span:nth-child(1){
/* ._title_et3n4_66{ */
font-size: 10px !important;
background-color: rgb(23, 23, 23, 0.045);
padding-inline: 16px;
font-size: 10px !important;
border-radius: 20px;
letter-spacing: .05em;
line-height: 34px !important;
color: #666666de;
}
.portal-0>div>div>ul{
/* ._container_et3n4_12>ul{ */
flex-direction: row-reverse;
margin: 0 !important;
}
.portal-0>div>div>ul>li{
/* ._container_et3n4_12>ul>li{ */
transform: rotate(90deg);
}
/* .fade-appeared>div{ */
._container_1t4jc_1{
max-height: 346px !important;
width:100% !important;
min-height: 346px !important;
border-radius:10px !important;
overflow:hidden;
height: 0 !important;
}
._scrollBody_15l2s_14>div>div:nth-child(2),._content_1t4jc_34>div>div:nth-child(2){
/* ._description_z6kdv_16{ */
color:rgb(23 23 23 / 50%) !important;
font-size:14px !important;
text-align: start;
}
._scrollBody_15l2s_14>div>div:nth-child(1),._content_1t4jc_34>div>div:nth-child(1){
/* ._title_z6kdv_7{ */
font-size: 30px !important;
text-align: start;
}
._decorationImg_1t4jc_27{
width: 312px !important;
}
._scrollContainer_15l2s_1{
height:auto !important;
background-color:#fff !important;
}
._scrollBox_15l2s_7{
padding-top:60px !important;
}
._closeIcon_1t4jc_17{
top: 13px !important;
right: 16px !important;
width: 47px !important;
height: 47px !important;
display:flex;
border-radius:50px;
align-items: center;
justify-content: center;
border:1px solid rgb(23 23 23 / 20%);
color:#000;
}
._closeIcon_1t4jc_17>svg{
fill:#fff;
}
._closeIcon_1t4jc_17>svg>path{
stroke: #000;
}
._closeIcon_1t4jc_17>svg>rect{
fill: white;
}
._scrollBody_15l2s_14>div>div:nth-child(3),._content_1t4jc_34>div>div:nth-child(3){
/* ._subscribebar_z6kdv_39{ */
display:none;
}
._bottomNode_15l2s_31{
text-align: start;
}
._bottomNode_15l2s_31>button{
/* ._button_1kajd_1{ */
width:auto !important;
background: #ffffff !important;
border:2px solid #000 !important;
border-radius:50px !important;
padding: 14px 20px 14px 20px !important;
}
._bottomNode_15l2s_31>button>span{
/* ._button_1kajd_1>span{ */
color:#000 !important;
font-size:16px !important;
font-weight: 100;
}
._scrollBody_15l2s_14{
overflow: visible !important;
}
._body_y2d1s_1{
max-width:790px !important;
width:100%;
}
@media screen and (max-width: 750px){
._body_y2d1s_1{
position: absolute !important;
bottom: 0;
left: 0;
}
._container_1t4jc_1 ._decorationImg_1t4jc_27 {
height: 0 !important;
}
._container_1t4jc_1{
border-radius: 30px 30px 0 0 !important;
text-align:left !important;
}
._bottomNode_1t4jc_43>ul{
margin:10px 0 0 0 !important;
}
._content_1t4jc_3>div>div:nth-child(1){
/* ._title_z6kdv_7{ */
font-size: 23px !important;
}
._content_1t4jc_3>div>div:nth-child(2){
/* ._description_z6kdv_16{ */
max-width:100% !important;
width:100% !important;
margin-top: 0 !important;
}
._content_1t4jc_34{
background-color:rgb(255,255,255) !important;
}
._container_1t4jc_1 ._content_1t4jc_34{
align-items: start;
}

}
/* 倒计时样式 */
.promotion-card-countdown{
    position: relative;
}
.card-countdown-prefix {
    position: absolute;
    right: 0;
    bottom: -20px;
    width: 156px;
    /* border: 1px solid; */
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
}
strong.notranslate>span:nth-child(1)::after {
    content: '';
    position: absolute;
    height: 30px;
    right: -2.5px;
    width: 11px;
    background: #2c2c2c;
}
strong.notranslate>span:nth-child(1) {
    overflow: hidden;
    background: #2c2c2c;
    color: #fff !important;
    padding: 7px;
    height: 10px;
    margin: 0 3px;
    display: inline-flex !important;
    width: 30px;
    height: 30px;
    overflow: hidden;
    justify-content: center;
    align-items: center;
    padding-left: 20px;
    border-radius: 4px;
    position: relative;
}
.notranslate > span.hidden,.notranslate > span.hidden+div{
    display: inline-flex !important;
}
/* 首页促销商品样式修改 */
a.promotion-product-item.show.newTheme{
    background-color: rgba(250, 250, 250);
    border-radius: var(--border-radius) !important;
    overflow: hidden;
    padding:0 !important;
}
.promotion-card-product-items.row.row-cols-2.row-cols-md-5.row-cols-lg-5.row-cols-xl-5 {
    gap: var(--grid-vertical-space) var(--grid-horizontal-space);
}
.promotion-product-item-title.product-grid-font {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
span.promotion-product-item-sale-price {
    margin-left: 12px;
}
span.sale-price {
    order: 1;
}
span.promotion-product-item-sale-price {
    font-size: var(--body2-font-size) !important;
}
.promotion-product-item-price.body-font.display- {
    display: flex;
    align-items: center;
    position: relative;
}
span.promotion-product-item-origin-price.notranslate {
    font-size: var(--body5-font-size)  !important;
	color: rgb(var(--color-light-text))  !important;
}
span.promotion-product-item-save-price {
     position: absolute;
     top: -76px;
    left: 10px;
    position: absolute;
    background-color: rgb(227, 38, 25);
    color: rgb(var(--card-badge-text-color, var(--color-discount-tag-text)));
    border-radius: var(--badge-border-radius);
     font-size: var(--body4-font-size);
   font-weight: var(--body-bold-font-weight) !important;
   padding:4px 8px !important;
}
/* 商品详情页+首页商品卡片评论去除margin */
.product-plugin-comment-rate-star {
  margin: 4px 0 4px 12px !important;
  padding-left: 0 !important;
  height: 14px !important;
}
.product-plugin-comment-rate-star > div {
  gap: 1px !important;
  height: 14px !important;
  line-height: 14px !important;
}
.product-plugin-comment-rate-star > div:last-child {
  margin-left: 4px !important;
  font-size: 12px !important;
}
.product-plugin-comment-rate-star > div:last-child > div:last-child {
  padding-left: 4px !important;
}
.product-plugin-comment-rate-star > div svg {
  width: 14px !important;
  height: 14px !important;
}
.product-plugin-comment-rate-star-half {
  line-height: 14px !important;
}
/* 移动端底部菜单栏icon大小调整 */
.advc .advc-footer-tab .advc-footer-tab__item .advc-footer-tab__icon{
  width:20px !important;
  height:22px !important;
}
p.advc-footer-tab__title.advc-title{
    font-size: 13px !important;
}
/* 商品折购价颜色改变 */
span.fw-bold.price-item.price-item--sale {
    color: rgb(225 29 72) !important;
}

/* ********搬迁自定义代码 */
/* 锚点定外z-index调整 */
#shopline-section-1737344535165bb56e30.advc-anchor-container {
    z-index: 9 !important;
}
/* 移动端底部菜单z-index */
.product-form__submit-span,.pay-button-buy-now-text{
  z-index:2 !important;
}


/* ********搬迁自定义代码 */
/* 全局商品推荐样式统一 */
.swiper-container.swiper-container-initialized.swiper-container-horizontal.swiper-container-pointer-events{
    cursor: auto !important;
}
span.sale-price:after {
    content: "税込";
    font-size: 0.7em;
    font-weight: normal;
    margin: 0 !important;
    position: relative;
    left: -3px;
}
.swiper-wrapper{
    overflow: auto;
    transform:none !important;
    scroll-behavior: smooth;
    gap:20px;
}
.swiper-wrapper>div{
    margin-right:0 !important;
}
.swiper-wrapper::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
}
span.sale-price {
font-size: 18px;
padding: 0 0 0 8px;
font-weight: 600;
}
.swiper-slide {
background: rgb(250, 250, 250);
border-radius:15px;
overflow:hidden;
width: 19% !important
}
.recommend-product-item-price{
margin:0 8px !important;
justify-content: start !important;
}
.product-grid-font{
-webkit-line-clamp:1 !important;
margin:0 8px !important;
font-size:16px !important;
}
.notranslate{
font-weight:600 !important;
font-size:17px;
}
/* .swiper-button-prev{
display:none;
} */
.recommend-product-item-info{
text-align:left;
}
@media screen and (max-width: 959px){
.swiper-container{
pointer-events: none;
}
.swiper-box{
overflow:auto;
}
.swiper-box::-webkit-scrollbar {
    display: none;
}
.swiper-container,.swiper-wrapper{
width: fit-content !important;
}
.swiper-slide{
width: calc((196vw - 20px) /3) !important;
margin-right:10px !important;;
}
.recommend-product-item{
max-width:none !important;;
}
.swiper-button-next{
display:none !important;;
}
}
.recommend-product-item-info .recommend-product-item-price>span.origin-price {
    text-decoration: none;
}
.recommend-product-item-info .recommend-product-item-price span.save-price {
    top: 8px;
    left: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 22.4px !important;
    line-height: 22.4px !important;
    z-index: 2;
    position: absolute;
    padding: 0 8px;
    color: #fff;
    border-radius: 40px;
    margin: 0 !important;
    font-weight: var(--body-bold-font-weight) !important;
    font-size: var(--body5-font-size) !important;
}
.recommend-product-item-info .recommend-product-item-price {
    position: inherit !important;
    margin: 0 8px 0 12px !important;
}
.recommend-product-item-info .recommend-product-item-price {
    flex-direction: row-reverse;
}
.sale-price {
    font-size: var(--body2-font-size) !important;
}

.recommend-product-item-info .recommend-product-item-price>span{
  margin:0 !important;
}
.recommend-product-item {
    margin-bottom: 10px !important;
}
@media screen and (min-width:960px){
.recommend-product-item-info {
    min-height: 76px;
}
}
.recommend-product-item-info .recommend-product-item-price{
  justify-content: start;
  position: relative;
}

span.origin-price.notranslate {
    text-decoration: line-through !important;
    color: rgb(var(--color-light-text)) !important;
    font-size: var(--body5-font-size) !important;
    display: flex;
    align-items: center;

}
.notranslate {
  font-size: var(--body5-font-size) !important;
}
.recommend-product-item-info .recommend-product-item-price>span.save-price {
    /* width: 100px; */
    text-align: center;
    background: var(--plugin-recommend-color_discount);
    color: #ffffff;
    font-size: 14px;
    font-weight: var(--body-bold-font-weight) !important;
    position: absolute;
    bottom: 70px;
    padding: 4px 8px;
}
span.recommend-product-item-sale-tag.body4 {
    display: none !important;
}
.recommend-product-item-price.body-font.display-center>span:nth-child(2){
  color:var(--plugin-recommend-color_discount) !important;
}
span.sale-price{
  padding:0 !important;
}
@media screen and (max-width:960px){
.sale-price {
    font-size: 12px !important;
    font-weight:600 !important;
}
span.origin-price.notranslate {
    font-size: var(--body5-font-size);
}
.recommend-product-item-info .recommend-product-item-price>span{
  margin:0px !important;
}
}
/* 按钮统一 */
.swiper-button-prev,
.swiper-button-next {
  top: 0 !important;
  left: auto !important;
  right: 70px !important;
}
.swiper-button-next {
  right: 10px !important;
}
.product-recommend .product-item-swiper-list .swiper-box .swiper-button-prev, .product-recommend .product-item-swiper-list .swiper-box .swiper-button-next {
    visibility: visible;
    opacity: 1;
}
.product-list-title.product-section-title.title5 {
    display: flex;
    justify-content: flex-start;
}
.swiper-button-prev.swiper-button-disabled, .swiper-button-next.swiper-button-disabled {
    opacity: 0.5 !important;
    color: rgba(var(--color-text), 0.3) !important;
}
.product-recommend .product-item-swiper-list .swiper-box:hover .swiper-button-prev.swiper-button-disabled, .product-recommend .product-item-swiper-list .swiper-box:hover .swiper-button-next.swiper-button-disabled {
    opacity: 0.5 !important;
    color: rgba(var(--color-text), 0.3) !important;
}
.product-recommend .product-item-swiper-list .swiper-box .swiper-button-prev:not(.swiper-button-disabled):hover, .product-recommend .product-item-swiper-list .swiper-box .swiper-button-next:not(.swiper-button-disabled):hover {
    transform: translateY(-50%) !important;
    box-shadow: none !important;
}
/* 全局商品推荐手机端样式 */
.recommend-grid{
    justify-content: space-around;
}

.recommend-product-item-info .recommend-product-item-price > span.sale-price {
      margin: 0px !important;
    }
.recommend-grid::-webkit-scrollbar {
    display: none;
}
#shopline-block-1740027732458085d69e > div > div > div > div.recommend-grid.recommend-grid-cols-2.recommend-grid-cols-4-desktop > div > a > div.recommend-product-item-info > div.recommend-product-item-price.body-font.display-center,#shopline-block-1740027732458085d69e > div > div > div > div.product-item-swiper-list > div > div.swiper-container.swiper-container-initialized.swiper-container-horizontal > div > div > a > div.recommend-product-item-info > div.recommend-product-item-price.body-font.display-center {
    flex-direction: row !important;
}
@media screen and (max-width:960px){
div.col{
    padding:0 !important;
    margin:0 0 10px 0 !important;
    border-radius:10px;
    overflow:hidden;
    width:46% !important;
    background: rgb(250, 250, 250);
}
.search-grid {
    justify-content: space-around !important;
}
.product-grid-font{
    font-size:14px !important;
}
.sale-price{
    font-size:15px !important;
}
.product-list-title.product-section-title.title5 {
    display: flex;
    justify-content: center !important;
}
}
button.btn.button.button--secondary.btn-outline-primary.btn-lg.btn-secondary.recomment-list-load-more-btn:after {
    content: "もっと見る";
    width: 100%;
    height: 100%;
    top: 50% !important;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    right: auto;
}
.product-recommend .recomment-list-load-more .recomment-list-load-more-btn{
    padding-left: 40px;
    padding-right: 40px;
}
/* 抽屉购物车【商品推荐】样式修改 */
#CartDrawer div.col{
    padding:0 !important;
    margin:0 0 10px 0 !important;
    border-radius:10px;
    overflow:hidden;
    width:46% !important;
    background: rgb(250, 250, 250);
}
#CartDrawer div.col .search-grid {
    justify-content: space-around !important;
}
#CartDrawer div.col .product-grid-font{
    font-size:14px !important;
}
#CartDrawer div.col span.sale-price:after {
    content: "税込";
    font-size: 0.7em;
    font-weight: normal;
    margin: 0 !important;
    position: relative;
    left: -3px;
}
#CartDrawer div.col .sale-price{
    font-size:15px !important;
    font-weight: 600 !important;
}
#CartDrawer .recommend-product-item {
    margin-bottom: 10px;
}
#CartDrawer div.col .product-grid-font {
    -webkit-line-clamp: 1 !important;
    margin: 0 8px !important;
}
#CartDrawer .recommend-grid {
    justify-content: space-around;
}
#CartDrawer div.col .recommend-product-item-price{
      justify-content: start !important;
}
#CartDrawer div.col .recommend-product-item-info .recommend-product-item-price {
    position: inherit !important;
    margin: 0 8px 0 12px !important;
}
#CartDrawer div.col .product-list-title.product-section-title.title5 {
    display: flex;
    justify-content: center !important;
}
#CartDrawer div.col .recommend-product-item-info {
    min-height: auto;
}
</style>
<script>







// 商品详细页推荐商品组件样式
function buttonShow(){
    document.querySelector('.swiper-button-next').addEventListener('click', function() {
    const slideWidth = document.querySelector('.swiper-slide').clientWidth;
    const wrapper = document.querySelector('.swiper-wrapper');
    // const moveDistance = slideWidth + 20; 
    const moveDistance = slideWidth * 5 + 100; 
    wrapper.scrollLeft += moveDistance;
});
document.querySelector('.swiper-button-prev').addEventListener('click', function() {
    const slideWidth = document.querySelector('.swiper-slide').clientWidth;
    const wrapper = document.querySelector('.swiper-wrapper');
    const moveDistance = slideWidth * 5 + 100;
    // const moveDistance = slideWidth + 20; 
    wrapper.scrollLeft -= moveDistance; 
});
};
window.addEventListener('scroll', function() {
    if (window.innerWidth >= 960) {
        buttonShow();
    }else{
    const swiperSlides = document.querySelectorAll('.swiper-slide');
    swiperSlides.forEach(slide => {
        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.right = '0';
        overlay.style.bottom = '0';
        overlay.style.cursor = 'pointer';
        overlay.style.background = 'rgba(255, 255, 255, 0)'; 
        overlay.style.pointerEvents = 'auto'; 
        overlay.onclick = function(event) {
            const link = slide.querySelector('a');
            if (link) {
                window.location.href = link.href; 
            }
        };
        slide.appendChild(overlay);
    });
    }
    const salePriceElements = document.querySelectorAll('.recommend-product-item .sale-price');
    salePriceElements.forEach(function(salePriceElement) {
        salePriceElement.textContent = salePriceElement.textContent.replace(/より/g, '');
    });
});
// 弹窗样式
document.addEventListener("DOMContentLoaded", function() {
var sourceContainer = 
`<ul class="footer-block__social_media-container">
  <li>
    <a
      href="https://www.facebook.com/people/CAGUUU%E6%A0%AA%E5%BC%8F%E4%BC%9A%E7%A4%BE/61560440906560/"
      target="_blank"
    >
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M26.5 14C26.5 20.9036 20.9036 26.5 14 26.5C7.09644 26.5 1.5 20.9036 1.5 14C1.5 7.09644 7.09644 1.5 14 1.5C20.9036 1.5 26.5 7.09644 26.5 14ZM28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14ZM13.692 7.2297C14.1483 7.04596 14.5361 7 14.9468 7L14.9467 7.02298H16.9771V9.27386H15.768C15.403 9.27386 15.1748 9.36574 15.0608 9.57244C14.9695 9.68732 14.9467 9.917 14.9467 10.2385V11.2491H17L16.7491 13.5H14.9468V20H12.3004V13.5H11V11.2491H12.3004V9.89402C12.3004 8.49298 12.7566 7.62015 13.692 7.2297Z"
          fill="currentColor"
        ></path>
      </svg>
    </a>
  </li>
  <li>
    <a href="https://x.com/CAGUUU_official?mx=2" target="_blank">
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="14" cy="14" r="13.25" stroke="currentColor" stroke-width="1.5"></circle>
        <path
          d="M15.0049 13.0934L19.3866 8H18.3483L14.5436 12.4225L11.5049 8H8L12.5952 14.6877L8 20.0289H9.03839L13.0562 15.3586L16.2654 20.0289H19.7703L15.0046 13.0934H15.0049ZM13.5827 14.7466L13.1171 14.0807L9.41254 8.78169H11.0074L13.9971 13.0581L14.4627 13.7241L18.3488 19.2828H16.7539L13.5827 14.7469V14.7466Z"
          fill="currentColor"
          stroke="currentColor"
          stroke-width="0.3"
        ></path>
      </svg>
    </a>
  </li>
  <li>
    <a href="https://www.instagram.com/caguuu_official/" target="_blank">
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M26.5 14C26.5 20.9036 20.9036 26.5 14 26.5C7.09644 26.5 1.5 20.9036 1.5 14C1.5 7.09644 7.09644 1.5 14 1.5C20.9036 1.5 26.5 7.09644 26.5 14ZM28 14C28 21.732 21.732 28 14 28C6.26801 28 0 21.732 0 14C0 6.26801 6.26801 0 14 0C21.732 0 28 6.26801 28 14ZM14 9.081C15.602 9.081 15.792 9.087 16.4245 9.116C16.8049 9.12052 17.1818 9.19037 17.5385 9.3225C17.7974 9.42235 18.0324 9.57528 18.2286 9.77144C18.4247 9.96761 18.5777 10.2027 18.6775 10.4615C18.8097 10.8183 18.8795 11.1951 18.884 11.5755C18.913 12.208 18.919 12.398 18.919 14.0005C18.919 15.603 18.913 15.792 18.884 16.4245C18.8795 16.8049 18.8097 17.1818 18.6775 17.5385C18.5777 17.7974 18.4247 18.0324 18.2286 18.2286C18.0324 18.4247 17.7974 18.5777 17.5385 18.6775C17.1818 18.8097 16.8049 18.8795 16.4245 18.884C15.792 18.913 15.602 18.919 14 18.919C12.398 18.919 12.208 18.913 11.5755 18.884C11.1951 18.8795 10.8183 18.8097 10.4615 18.6775C10.2027 18.5777 9.96761 18.4247 9.77144 18.2286C9.57528 18.0324 9.42235 17.7974 9.3225 17.5385C9.19037 17.1818 9.12052 16.8049 9.116 16.4245C9.087 15.792 9.081 15.602 9.081 14C9.081 12.398 9.087 12.208 9.116 11.5755C9.12052 11.1951 9.19037 10.8183 9.3225 10.4615C9.42235 10.2027 9.57528 9.96761 9.77144 9.77144C9.96761 9.57528 10.2027 9.42235 10.4615 9.3225C10.8183 9.19037 11.1951 9.12052 11.5755 9.116C12.208 9.087 12.398 9.081 14 9.081ZM14 8C12.3705 8 12.166 8.007 11.526 8.036C11.0284 8.04605 10.5361 8.14038 10.07 8.315C9.67137 8.46915 9.30933 8.70489 9.00711 9.00711C8.70489 9.30933 8.46915 9.67137 8.315 10.07C8.14033 10.5362 8.04599 11.0287 8.036 11.5265C8.007 12.1665 8 12.37 8 14C8 15.63 8.007 15.834 8.036 16.474C8.04605 16.9716 8.14038 17.4639 8.315 17.93C8.46915 18.3287 8.70489 18.6907 9.00711 18.9929C9.30933 19.2951 9.67137 19.5309 10.07 19.685C10.5362 19.8597 11.0287 19.954 11.5265 19.964C12.1665 19.993 12.3705 20 14 20C15.6295 20 15.834 19.993 16.474 19.964C16.9718 19.954 17.4643 19.8597 17.9305 19.685C18.3292 19.5309 18.6912 19.2951 18.9934 18.9929C19.2956 18.6907 19.5314 18.3287 19.6855 17.93C19.86 17.4638 19.9542 16.9713 19.964 16.4735C19.993 15.8335 20 15.63 20 14C20 12.37 19.993 12.166 19.964 11.526C19.954 11.0284 19.8596 10.5361 19.685 10.07C19.5309 9.67137 19.2951 9.30933 18.9929 9.00711C18.6907 8.70489 18.3287 8.46915 17.93 8.315C17.4638 8.1405 16.9713 8.04633 16.4735 8.0365C15.8335 8.0065 15.63 8 14 8ZM17.75 10.9999C17.75 11.4141 17.4142 11.7499 17 11.7499C16.5858 11.7499 16.25 11.4141 16.25 10.9999C16.25 10.5857 16.5858 10.2499 17 10.2499C17.4142 10.2499 17.75 10.5857 17.75 10.9999ZM12.3333 11.5055C12.8266 11.1758 13.4067 10.9999 14 10.9999C14.7957 10.9999 15.5587 11.316 16.1213 11.8786C16.6839 12.4412 17 13.2043 17 13.9999C17 14.5932 16.8241 15.1733 16.4944 15.6666C16.1648 16.16 15.6962 16.5445 15.1481 16.7715C14.5999 16.9986 13.9967 17.058 13.4147 16.9423C12.8328 16.8265 12.2982 16.5408 11.8787 16.1212C11.4591 15.7017 11.1734 15.1671 11.0576 14.5852C10.9419 14.0032 11.0013 13.4 11.2284 12.8518C11.4554 12.3037 11.8399 11.8351 12.3333 11.5055ZM12.9181 15.6191C13.2383 15.8331 13.6148 15.9473 14 15.9473C14.5165 15.9473 15.0118 15.7422 15.377 15.3769C15.7423 15.0117 15.9474 14.5164 15.9474 13.9999C15.9474 13.6147 15.8332 13.2382 15.6192 12.918C15.4052 12.5977 15.1011 12.3481 14.7453 12.2007C14.3894 12.0533 13.9978 12.0148 13.6201 12.0899C13.2423 12.165 12.8953 12.3505 12.623 12.6229C12.3506 12.8952 12.1651 13.2422 12.09 13.62C12.0149 13.9977 12.0534 14.3893 12.2008 14.7451C12.3482 15.101 12.5978 15.4051 12.9181 15.6191Z"
          fill="currentColor"
        ></path>
      </svg>

    </a>
  </li>
  <li>
    <a href="https://page.line.me/?accountId=795veuer" target="_blank">
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="14" cy="14" r="13.25" stroke="currentColor" stroke-width="1.5"></circle>
        <path
          d="M14.0002 8.00003C10.134 8.00003 7 10.3883 7 13.3341C7 16.0704 9.70526 18.3242 13.1888 18.6312C13.2464 18.6407 13.3014 18.6616 13.3504 18.6925C13.3993 18.7234 13.4412 18.7638 13.4733 18.8111C13.6279 19.0575 13.518 19.5826 13.398 20.1714C13.2779 20.7601 13.9308 20.4545 14.0753 20.3864C14.1903 20.3324 17.1499 18.7173 18.9118 17.1334C20.2003 16.1657 21 14.8211 21 13.3343C21.0004 10.3883 17.8662 8.00003 14.0002 8.00003ZM11.6668 14.7301C11.6659 14.8259 11.6258 14.9174 11.5552 14.9846C11.4846 15.0517 11.3894 15.089 11.2905 15.0882H10.0107C9.78482 15.0882 9.48382 15.0127 9.48382 14.6584V12.0094C9.48423 11.9619 9.4943 11.915 9.51343 11.8714C9.53257 11.8277 9.56041 11.7881 9.59535 11.7548C9.63029 11.7216 9.67166 11.6953 9.7171 11.6775C9.76253 11.6598 9.81113 11.6508 9.86013 11.6512H9.93544C9.98444 11.6508 10.033 11.6598 10.0785 11.6775C10.1239 11.6953 10.1653 11.7216 10.2002 11.7548C10.2352 11.7881 10.263 11.8277 10.2821 11.8714C10.3013 11.915 10.3113 11.9619 10.3118 12.0094V14.3003H11.2903C11.3893 14.2995 11.4845 14.3367 11.5551 14.4039C11.6257 14.4711 11.6659 14.5626 11.6668 14.6584V14.7301H11.6668ZM12.7217 14.6588C12.7217 14.7554 12.6821 14.8479 12.6116 14.9162C12.5411 14.9844 12.4455 15.0227 12.3458 15.0227C12.2461 15.0227 12.1505 14.9844 12.08 14.9162C12.0095 14.8479 11.9699 14.7554 11.9699 14.6588V12.0853C11.9699 11.9888 12.0095 11.8963 12.08 11.828C12.1505 11.7598 12.2461 11.7215 12.3458 11.7215C12.4455 11.7215 12.5411 11.7598 12.6116 11.828C12.6821 11.8963 12.7217 11.9888 12.7217 12.0853V14.6588ZM16.0326 14.6588C16.0224 14.7515 15.9792 14.8378 15.9106 14.9031C15.842 14.9684 15.7521 15.0086 15.6563 15.017C15.5695 15.0313 15.4803 15.0193 15.4008 14.9825C15.3214 14.9457 15.2557 14.886 15.2127 14.8116L14.0002 13.1653V14.6584C13.9979 14.7535 13.9572 14.8439 13.8869 14.9104C13.8166 14.9768 13.7222 15.014 13.624 15.014C13.5258 15.014 13.4314 14.9768 13.3611 14.9104C13.2908 14.8439 13.2501 14.7535 13.2478 14.6584V12.0808C13.2482 12.0334 13.2583 11.9865 13.2774 11.9429C13.2965 11.8993 13.3243 11.8597 13.3593 11.8264C13.3942 11.7932 13.4355 11.767 13.4809 11.7492C13.5263 11.7314 13.5749 11.7225 13.6239 11.7229C13.7133 11.7234 13.8007 11.7483 13.8761 11.7947C13.9515 11.8411 14.012 11.9071 14.0505 11.9851C14.1671 12.1519 15.2808 13.6551 15.2808 13.6551V12.0808C15.2832 11.9857 15.324 11.8953 15.3943 11.8289C15.4646 11.7625 15.559 11.7253 15.6572 11.7253C15.7555 11.7253 15.8499 11.7625 15.9202 11.8289C15.9905 11.8953 16.0312 11.9857 16.0337 12.0808V14.6584L16.0326 14.6588ZM18.2907 13.0121C18.3393 13.0121 18.3874 13.0214 18.4323 13.0394C18.4772 13.0574 18.5179 13.0838 18.5523 13.117C18.5867 13.1503 18.6139 13.1897 18.6325 13.2332C18.6511 13.2766 18.6607 13.3232 18.6607 13.3702C18.6607 13.4173 18.6511 13.4638 18.6325 13.5073C18.6139 13.5507 18.5867 13.5902 18.5523 13.6235C18.5179 13.6567 18.4772 13.6831 18.4323 13.7011C18.3874 13.7191 18.3393 13.7284 18.2907 13.7284H17.3121V14.3011H18.2907C18.34 14.3 18.389 14.3084 18.4349 14.3259C18.4808 14.3434 18.5226 14.3696 18.5579 14.403C18.5931 14.4363 18.6212 14.4762 18.6403 14.5202C18.6594 14.5642 18.6693 14.6114 18.6693 14.6592C18.6693 14.7069 18.6594 14.7541 18.6403 14.7981C18.6212 14.8421 18.5931 14.882 18.5579 14.9153C18.5226 14.9487 18.4808 14.9749 18.4349 14.9924C18.389 15.0099 18.34 15.0183 18.2907 15.0172H16.823C16.7788 15.0177 16.735 15.0097 16.6941 14.9938C16.6531 14.9779 16.6158 14.9543 16.5843 14.9244C16.5527 14.8945 16.5276 14.8588 16.5103 14.8195C16.4931 14.7802 16.484 14.7379 16.4836 14.6952V12.0455C16.4839 12.0028 16.493 11.9605 16.5103 11.9211C16.5275 11.8818 16.5527 11.8461 16.5842 11.8162C16.6157 11.7863 16.6531 11.7627 16.694 11.7467C16.735 11.7308 16.7788 11.7228 16.823 11.7233H18.2907C18.34 11.7222 18.389 11.7306 18.4349 11.7481C18.4808 11.7656 18.5226 11.7918 18.5579 11.8252C18.5931 11.8585 18.6212 11.8984 18.6403 11.9424C18.6594 11.9864 18.6693 12.0336 18.6693 12.0813C18.6693 12.1291 18.6594 12.1763 18.6403 12.2203C18.6212 12.2643 18.5931 12.3042 18.5579 12.3375C18.5226 12.3709 18.4808 12.3971 18.4349 12.4146C18.389 12.4321 18.34 12.4405 18.2907 12.4394H17.3121V13.0123H18.2907L18.2907 13.0121Z"
          fill="currentColor"
        ></path>
      </svg>

    </a>
  </li>
</ul>`;
var tempDiv = document.createElement('div');
tempDiv.innerHTML = sourceContainer;
setTimeout(() => {
const targetContainer = document.querySelector('.portal-0>div>div');
if(targetContainer){
targetContainer.appendChild(tempDiv.firstChild);
if(document.querySelector('.portal-0>div>div>span:nth-child(1)')){
document.querySelector('.portal-0>div>div>span:nth-child(1)').addEventListener('click', function() {
setTimeout(() => {
const width = window.innerWidth;

if (width <= 750) {
// 如果窗口宽度小于等于750
tempDiv.innerHTML = sourceContainer;
document.querySelector('._bottomNode_1t4jc_43').appendChild(tempDiv.firstChild);
} else {
// 如果窗口宽度大于750
const button_div = document.querySelector('._bottomNode_15l2s_31');
tempDiv.innerHTML = sourceContainer;
button_div.appendChild(tempDiv.firstChild);
}
// 为注册按钮添加点击事件
document.querySelector('._bottomNode_15l2s_31>button').addEventListener('click', function() {
window.location.href = '/user/signUp';
});
}, 1000);
});
}

}
}, 1000);
});
{{!-- 首页倒计时 --}}
    const countdownContainer = document.querySelector('.promotion-card-countdown');
    if(countdownContainer){
    const countdownPrefix = document.createElement('div');
    countdownPrefix.className = 'card-countdown-prefix';
    countdownPrefix.style.color = '#2c2c2c';
    const dayDiv = document.createElement('div');
    dayDiv.textContent = '日';
    const hoursDiv = document.createElement('div');
    hoursDiv.textContent = '時';
    const minutesDiv = document.createElement('div');
    minutesDiv.textContent = '分';
    const secondsDiv = document.createElement('div');
    secondsDiv.textContent = '秒';
    countdownPrefix.appendChild(dayDiv);
    countdownPrefix.appendChild(hoursDiv);
    countdownPrefix.appendChild(minutesDiv);
    countdownPrefix.appendChild(secondsDiv);
    countdownContainer.appendChild(countdownPrefix);
    };

function checkWidth() {
    if (window.innerWidth >= 960) {
//首页【推荐商品】图片轮播展示
function initializeMouseEvents() {
    const productBlocks = document.querySelectorAll('slider-component > ul > li.slider__slide');

    if (productBlocks.length > 0) {
        productBlocks.forEach(block => {
            setTimeout(() => {
                const cardInnerWrapper = block.querySelector('.card__inner--wrapper');
                if(cardInnerWrapper){
                const mediaLink = cardInnerWrapper.querySelector('.card__media');
                const showSmallLis = block.querySelectorAll('.show_small_li');

                cardInnerWrapper.addEventListener('mousemove', (event) => {
                    const width = mediaLink.querySelector('.collection-hero__image').getBoundingClientRect().width; 
                    const mouseX = event.clientX - cardInnerWrapper.getBoundingClientRect().left;

                    showSmallLis.forEach(li => li.classList.remove('show_small_li_active'));

                    if (mouseX < width / 3) {
                        mediaLink.style.transform = `translate(-${width}px, 0)`;
                        showSmallLis[0].classList.add('show_small_li_active');
                    } else if (mouseX < (2 * width) / 3) {
                        mediaLink.style.transform = `translate(-${2 * width}px, 0)`;
                        showSmallLis[1].classList.add('show_small_li_active');
                    } else {
                        mediaLink.style.transform = `translate(-${3 * width}px, 0)`; 
                        showSmallLis[2].classList.add('show_small_li_active');
                    }
                });

                cardInnerWrapper.addEventListener('mouseleave', () => {
                    mediaLink.style.transform = 'translate(0, 0)'; 
                    showSmallLis.forEach(li => li.classList.remove('show_small_li_active'));
                });
                }

            }, 1500);
        });
    }
}

const observer = new MutationObserver(() => {
    const sliderComponent = document.querySelector('slider-component > ul');
    if (sliderComponent) {
        initializeMouseEvents();
    }
});

observer.observe(document.body, { childList: true, subtree: true });
    }
}

checkWidth();

window.addEventListener('resize', checkWidth);


      window.shopUrl = '{{ request.origin }}';
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        cart_url: '{{ routes.cart_url }}',
        cart_count_url: '{{ routes.cart_count_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}',
        account_url: '{{ routes.account_url }}',
        account_login_url: '{{ routes.account_login_url }}',
        address_url: '{{ routes.address_url }}',
        cart_discount_code_apply_url: '{{ routes.cart_discount_code_apply_url }}',
        cart_discount_code_remove_url: '{{ routes.cart_discount_code_remove_url }}',
      };
      window.__I18N__ = window.__I18N__ || {};
      window.__I18N__['unvisiable'] = {{{ json (t 'unvisiable') }}};
      window.__I18N__['cart'] = {{{ json (t 'cart') }}};
      window.__I18N__['products'] = {{{ json (t 'products') }}};
    </script>
    <script>
{{!-- ********搬迁自定义代码 --}}
{{!-- 超级菜单点击优化 --}}
setTimeout(()=>{  
const menuItems = document.querySelectorAll('._pcMenu_wsyqi_1.mega-pc-item');
menuItems.forEach(item => {
    item.addEventListener('click', () => {
        const link = item.querySelector('a');
        if (link) {
            window.location.href = link.href;
        }
    });
});
},2000);

// ********搬迁自定义代码
// 抽屉购物车【商品推荐】样式修改
const cartDrawerInner = document.querySelector('.cart-drawer__inner');
if (cartDrawerInner) {
  cartDrawerInner.addEventListener('scroll', () => {
    const salePriceElements = document.querySelectorAll('#CartDrawer div.col .sale-price');
    salePriceElements.forEach(function(salePriceElement) {
      salePriceElement.textContent = salePriceElement.textContent.replace(/より/g, '');
    });
  });
}
//20250414改文案为税込・送料無料
let changeWenziId = setInterval(()=>{
    //console.log("改一下这个文案为税込・送料無料");
    const divElements = document.getElementsByClassName("_tip_v48v8_31");
  // 选择第一个元素并修改内容
  if(divElements[0]){
    divElements[0].textContent = "税込・送料無料";
  }
},500);
setTimeout(() => {
    clearInterval(changeWenziId);
    //console.log("setInterval 已停止执行");
}, 10000); // 10000 毫秒（10 秒）后执行

    </script>
    <script src="{{asset_url 'cg-discounts.js'}}"></script>
  </body>
</html>